// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery_payment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeliveryPaymentState {
  bool get isLoading => throw _privateConstructorUsedError;
  List<DeliveryPaymentMethod> get methods => throw _privateConstructorUsedError;
  String get instructions => throw _privateConstructorUsedError;
  String get error => throw _privateConstructorUsedError;
  int get selectedIndex => throw _privateConstructorUsedError;
  bool get changeRequired => throw _privateConstructorUsedError;
  double get changeAmount => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryPaymentStateCopyWith<DeliveryPaymentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryPaymentStateCopyWith<$Res> {
  factory $DeliveryPaymentStateCopyWith(DeliveryPaymentState value,
          $Res Function(DeliveryPaymentState) then) =
      _$DeliveryPaymentStateCopyWithImpl<$Res, DeliveryPaymentState>;
  @useResult
  $Res call(
      {bool isLoading,
      List<DeliveryPaymentMethod> methods,
      String instructions,
      String error,
      int selectedIndex,
      bool changeRequired,
      double changeAmount});
}

/// @nodoc
class _$DeliveryPaymentStateCopyWithImpl<$Res,
        $Val extends DeliveryPaymentState>
    implements $DeliveryPaymentStateCopyWith<$Res> {
  _$DeliveryPaymentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? methods = null,
    Object? instructions = null,
    Object? error = null,
    Object? selectedIndex = null,
    Object? changeRequired = null,
    Object? changeAmount = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      methods: null == methods
          ? _value.methods
          : methods // ignore: cast_nullable_to_non_nullable
              as List<DeliveryPaymentMethod>,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
      changeRequired: null == changeRequired
          ? _value.changeRequired
          : changeRequired // ignore: cast_nullable_to_non_nullable
              as bool,
      changeAmount: null == changeAmount
          ? _value.changeAmount
          : changeAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryPaymentStateImplCopyWith<$Res>
    implements $DeliveryPaymentStateCopyWith<$Res> {
  factory _$$DeliveryPaymentStateImplCopyWith(_$DeliveryPaymentStateImpl value,
          $Res Function(_$DeliveryPaymentStateImpl) then) =
      __$$DeliveryPaymentStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      List<DeliveryPaymentMethod> methods,
      String instructions,
      String error,
      int selectedIndex,
      bool changeRequired,
      double changeAmount});
}

/// @nodoc
class __$$DeliveryPaymentStateImplCopyWithImpl<$Res>
    extends _$DeliveryPaymentStateCopyWithImpl<$Res, _$DeliveryPaymentStateImpl>
    implements _$$DeliveryPaymentStateImplCopyWith<$Res> {
  __$$DeliveryPaymentStateImplCopyWithImpl(_$DeliveryPaymentStateImpl _value,
      $Res Function(_$DeliveryPaymentStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? methods = null,
    Object? instructions = null,
    Object? error = null,
    Object? selectedIndex = null,
    Object? changeRequired = null,
    Object? changeAmount = null,
  }) {
    return _then(_$DeliveryPaymentStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      methods: null == methods
          ? _value._methods
          : methods // ignore: cast_nullable_to_non_nullable
              as List<DeliveryPaymentMethod>,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
      changeRequired: null == changeRequired
          ? _value.changeRequired
          : changeRequired // ignore: cast_nullable_to_non_nullable
              as bool,
      changeAmount: null == changeAmount
          ? _value.changeAmount
          : changeAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$DeliveryPaymentStateImpl extends _DeliveryPaymentState {
  const _$DeliveryPaymentStateImpl(
      {this.isLoading = false,
      final List<DeliveryPaymentMethod> methods = const [],
      this.instructions = '',
      this.error = '',
      this.selectedIndex = -1,
      this.changeRequired = false,
      this.changeAmount = 0.0})
      : _methods = methods,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  final List<DeliveryPaymentMethod> _methods;
  @override
  @JsonKey()
  List<DeliveryPaymentMethod> get methods {
    if (_methods is EqualUnmodifiableListView) return _methods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_methods);
  }

  @override
  @JsonKey()
  final String instructions;
  @override
  @JsonKey()
  final String error;
  @override
  @JsonKey()
  final int selectedIndex;
  @override
  @JsonKey()
  final bool changeRequired;
  @override
  @JsonKey()
  final double changeAmount;

  @override
  String toString() {
    return 'DeliveryPaymentState(isLoading: $isLoading, methods: $methods, instructions: $instructions, error: $error, selectedIndex: $selectedIndex, changeRequired: $changeRequired, changeAmount: $changeAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryPaymentStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            const DeepCollectionEquality().equals(other._methods, _methods) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex) &&
            (identical(other.changeRequired, changeRequired) ||
                other.changeRequired == changeRequired) &&
            (identical(other.changeAmount, changeAmount) ||
                other.changeAmount == changeAmount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      const DeepCollectionEquality().hash(_methods),
      instructions,
      error,
      selectedIndex,
      changeRequired,
      changeAmount);

  /// Create a copy of DeliveryPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryPaymentStateImplCopyWith<_$DeliveryPaymentStateImpl>
      get copyWith =>
          __$$DeliveryPaymentStateImplCopyWithImpl<_$DeliveryPaymentStateImpl>(
              this, _$identity);
}

abstract class _DeliveryPaymentState extends DeliveryPaymentState {
  const factory _DeliveryPaymentState(
      {final bool isLoading,
      final List<DeliveryPaymentMethod> methods,
      final String instructions,
      final String error,
      final int selectedIndex,
      final bool changeRequired,
      final double changeAmount}) = _$DeliveryPaymentStateImpl;
  const _DeliveryPaymentState._() : super._();

  @override
  bool get isLoading;
  @override
  List<DeliveryPaymentMethod> get methods;
  @override
  String get instructions;
  @override
  String get error;
  @override
  int get selectedIndex;
  @override
  bool get changeRequired;
  @override
  double get changeAmount;

  /// Create a copy of DeliveryPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryPaymentStateImplCopyWith<_$DeliveryPaymentStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
