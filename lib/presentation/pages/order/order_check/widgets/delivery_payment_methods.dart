import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_provider.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_state.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/components/buttons/custom_button.dart';
import 'package:pandoo_delivery/presentation/components/loading.dart';
import 'package:pandoo_delivery/presentation/components/title_icon.dart';
import 'package:pandoo_delivery/presentation/components/text_fields/custom_text_field.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'delivery_payment_method_item.dart';
import 'change_option_button.dart';

class DeliveryPaymentMethods extends ConsumerStatefulWidget {
  final int? shopId;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const DeliveryPaymentMethods({
    super.key,
    this.shopId,
    this.onPaymentSelected,
  });

  @override
  ConsumerState<DeliveryPaymentMethods> createState() => _DeliveryPaymentMethodsState();
}

class _DeliveryPaymentMethodsState extends ConsumerState<DeliveryPaymentMethods> {
  final TextEditingController changeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.shopId != null) {
        ref.read(deliveryPaymentProvider.notifier)
            .fetchDeliveryPaymentMethods(context, widget.shopId!);
      }
    });
  }

  @override
  void dispose() {
    changeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(deliveryPaymentProvider);
    final notifier = ref.read(deliveryPaymentProvider.notifier);

    return Container(
      decoration: BoxDecoration(
        color: AppStyle.bgGrey.withOpacity(0.96),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      width: double.infinity,
      child: state.isLoading
          ? const Loading()
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Drag Handle
                    Container(
                      height: 4.h,
                      width: 48.w,
                      decoration: BoxDecoration(
                        color: AppStyle.dragElement,
                        borderRadius: BorderRadius.all(Radius.circular(40.r)),
                      ),
                    ),

                    14.verticalSpace,

                    // Header
                    TitleAndIcon(
                      title: AppHelpers.getTranslation(TrKeys.selectDeliveryPayment),
                    ),

                    24.verticalSpace,

                    // Payment Methods List
                    if (state.methods.isNotEmpty)
                      ...state.methods.asMap().entries.map((entry) {
                        final index = entry.key;
                        final method = entry.value;

                        return DeliveryPaymentMethodItem(
                          method: method,
                          isSelected: state.selectedIndex == index,
                          onTap: () => notifier.selectPaymentMethod(index),
                        );
                      })
                    else
                      Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 32.h),
                          child: Text(
                            AppHelpers.getTranslation(TrKeys.noDeliveryPaymentMethods),
                            style: AppStyle.interSemi(
                              size: 16,
                              color: AppStyle.textGrey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                    // Change Amount Input (shown only for cash)
                    if (_isCashSelected(state)) ...[
                      24.verticalSpace,
                      _buildChangeSection(state, notifier),
                    ],

                    32.verticalSpace,

                    // Confirm Button
                    CustomButton(
                      title: AppHelpers.getTranslation(TrKeys.confirm),
                      onPressed: _isValidSelection(state) ? () => _confirmSelection(state) : null,
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildChangeSection(DeliveryPaymentState state, notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppHelpers.getTranslation(TrKeys.needChange),
          style: AppStyle.interNormal(size: 16),
        ),

        16.verticalSpace,

        // Change Required Toggle
        Row(
          children: [
            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.yes),
                isSelected: state.changeRequired,
                onTap: () {
                  notifier.setChangeRequired(true);
                },
              ),
            ),

            12.horizontalSpace,

            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.no),
                isSelected: !state.changeRequired,
                onTap: () {
                  notifier.setChangeRequired(false);
                  changeController.clear();
                },
              ),
            ),
          ],
        ),

        // Change Amount Input
        if (state.changeRequired) ...[
          16.verticalSpace,
          Text(
            AppHelpers.getTranslation(TrKeys.changeForAmount),
            style: AppStyle.interNormal(size: 14),
          ),
          8.verticalSpace,
          CustomTextField(
            controller: changeController,
            keyboardType: TextInputType.number,
            prefix: Icon(Icons.attach_money, color: AppStyle.primary),
            hint: '0,00',
            onChanged: (value) {
              final amount = double.tryParse(value.replaceAll(',', '.')) ?? 0.0;
              notifier.setChangeAmount(amount);
            },
          ),
        ],
      ],
    );
  }

  bool _isCashSelected(DeliveryPaymentState state) {
    if (state.selectedIndex == -1 || state.methods.isEmpty) return false;
    return state.methods[state.selectedIndex].tag == 'cash_delivery';
  }

  bool _isValidSelection(DeliveryPaymentState state) {
    if (state.selectedIndex == -1) return false;
    if (_isCashSelected(state) && state.changeRequired && state.changeAmount <= 0) return false;
    return true;
  }

  void _confirmSelection(DeliveryPaymentState state) {
    final selectedMethod = state.methods[state.selectedIndex];

    final paymentData = {
      'payment_method': selectedMethod.tag,
      'change_required': _isCashSelected(state) ? state.changeRequired : false,
      'change_amount': _isCashSelected(state) ? state.changeAmount : null,
      'payment_notes': null,
    };

    widget.onPaymentSelected?.call(paymentData);
    context.maybePop();
  }
}
